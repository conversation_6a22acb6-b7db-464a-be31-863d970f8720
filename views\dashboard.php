<?php

    require '../config.php';
    require '../controllers/dashboard.php';

    if (!isset($_SESSION['user'])) {
        header("Location: login.php");
        exit;
    }

    $user = $_SESSION['user'];

    $total = totalCurrentMonth($conn);
    $lastTotal = totalLastMonth($conn);
    $max = maxCurrentMonth($conn);

    $typeSelected = $_POST['type'] ?? 'revenu';


    
    $categories = listCategories($typeSelected, $conn);

    $result;

    if (isset($_POST['afficher']) && isset($_POST['categorie'])) {
        $categorieId = $_POST['categorie'];
    
        if ($typeSelected === 'revenu') {
            $result = totalIncomesByCategory($categorieId, $conn);
        } else {
            $result = totalExpensesByCategory($categorieId, $conn);
        }
    }
    

?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Tableau de bord</title>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.1.2/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .gradient-income {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .gradient-expense {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .gradient-balance {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .gradient-max {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .animate-fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        .animate-slide-up {
            animation: slideUp 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen font-sans">
    <?php  require '../header.php'; ?>

    <!-- Page Header -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="animate-fade-in">
            <h1 class="text-4xl font-bold text-gray-900 mb-2">Tableau de bord</h1>
            <p class="text-lg text-gray-600">Bienvenue, <?= $_SESSION['user']['nom'] ?>. Voici un aperçu de vos finances.</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

            <!-- Total Balance Card -->
            <div class="gradient-balance rounded-2xl shadow-xl p-6 card-hover animate-slide-up text-white relative overflow-hidden">
                <div class="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-20 rounded-full"></div>
                <div class="relative z-10">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 bg-white bg-opacity-20 rounded-xl">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-white text-opacity-90 mb-1">Solde Total</h3>
                    <p class="text-3xl font-bold text-white"><?= number_format(soldUser($conn), 2) ?> Dh</p>
                </div>
            </div>

            <!-- Total Period Income Card -->
            <div class="gradient-income rounded-2xl shadow-xl p-6 card-hover animate-slide-up text-white relative overflow-hidden" style="animation-delay: 0.1s;">
                <div class="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-20 rounded-full"></div>
                <div class="relative z-10">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 bg-white bg-opacity-20 rounded-xl">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                        <?php
                            if ($total['revenu'] != 0) {
                                $perRevenu = number_format(($total['revenu'] - $lastTotal['revenu']) / $total['revenu'] * 100, 2);
                            } else {
                                $perRevenu = 0;
                            }
                        ?>
                        <?php if ($perRevenu != 0): ?>
                            <div class="flex items-center text-sm font-semibold bg-white bg-opacity-20 px-3 py-1 rounded-full">
                                <?php if($perRevenu > 0): ?>
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                                    </svg>
                                    +<?= $perRevenu ?>%
                                <?php else: ?>
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clip-rule="evenodd"></path>
                                    </svg>
                                    <?= $perRevenu ?>%
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <h3 class="text-sm font-medium text-white text-opacity-90 mb-1">Revenus ce mois</h3>
                    <p class="text-3xl font-bold text-white mb-2"><?= number_format($total['revenu'], 2) ?> Dh</p>
                    <p class="text-sm text-white text-opacity-75">Mois dernier: <?= number_format($lastTotal['revenu'], 2) ?> Dh</p>
                </div>
            </div>

            <!-- Total Period Expenses Card -->
            <div class="gradient-expense rounded-2xl shadow-xl p-6 card-hover animate-slide-up text-white relative overflow-hidden" style="animation-delay: 0.2s;">
                <div class="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-20 rounded-full"></div>
                <div class="relative z-10">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 bg-white bg-opacity-20 rounded-xl">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                            </svg>
                        </div>
                        <?php
                            if ($total['depense'] != 0) {
                                $perDepense = number_format(($total['depense'] - $lastTotal['depense']) / $total['depense'] * 100, 2);
                            } else {
                                $perDepense = 0;
                            }
                        ?>
                        <?php if ($perDepense != 0): ?>
                            <div class="flex items-center text-sm font-semibold bg-white bg-opacity-20 px-3 py-1 rounded-full">
                                <?php if($perDepense < 0): ?>
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                                    </svg>
                                    <?= abs($perDepense) ?>%
                                <?php else: ?>
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clip-rule="evenodd"></path>
                                    </svg>
                                    +<?= $perDepense ?>%
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <h3 class="text-sm font-medium text-white text-opacity-90 mb-1">Dépenses ce mois</h3>
                    <p class="text-3xl font-bold text-white mb-2"><?= number_format($total['depense'], 2) ?> Dh</p>
                    <p class="text-sm text-white text-opacity-75">Mois dernier: <?= number_format($lastTotal['depense'], 2) ?> Dh</p>
                </div>
            </div>

            <!-- Max Transactions Card -->
            <div class="gradient-max rounded-2xl shadow-xl p-6 card-hover animate-slide-up text-white relative overflow-hidden" style="animation-delay: 0.3s;">
                <div class="absolute top-0 right-0 -mt-4 -mr-4 w-20 h-20 bg-white bg-opacity-20 rounded-full"></div>
                <div class="relative z-10">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 bg-white bg-opacity-20 rounded-xl">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <h3 class="text-sm font-medium text-white text-opacity-90 mb-3">Transactions maximales</h3>
                    <div class="space-y-3">
                        <div class="bg-white bg-opacity-20 rounded-lg p-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-white text-opacity-90">Plus grand revenu</span>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="font-bold"><?= number_format($max['revenu'], 2) ?> Dh</span>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white bg-opacity-20 rounded-lg p-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-white text-opacity-90">Plus grande dépense</span>
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clip-rule="evenodd"></path>
                                    </svg>
                                    <span class="font-bold"><?= number_format($max['depense'], 2) ?> Dh</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Filter Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
        <div class="glass-effect rounded-3xl shadow-2xl p-8 animate-fade-in">
            <div class="flex items-center mb-6">
                <div class="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">Filtrer par catégorie</h3>
            </div>

            <form method="POST" class="space-y-6">
                <div>
                    <p class="text-sm font-semibold mb-4 text-gray-700">Type de transaction:</p>
                    <div class="inline-flex bg-gray-100 rounded-2xl p-1 shadow-inner">
                        <label class="relative px-6 py-3 font-medium cursor-pointer transition-all duration-300 rounded-xl <?= $typeSelected === 'revenu' ? 'bg-white text-green-600 shadow-lg' : 'text-gray-600 hover:text-green-600' ?>">
                            <input type="radio" name="type" value="revenu" onchange="this.form.submit()" class="hidden" <?= $typeSelected === 'revenu' ? 'checked' : '' ?>>
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                Revenu
                            </span>
                        </label>
                        <label class="relative px-6 py-3 font-medium cursor-pointer transition-all duration-300 rounded-xl <?= $typeSelected === 'depense' ? 'bg-white text-red-600 shadow-lg' : 'text-gray-600 hover:text-red-600' ?>">
                            <input type="radio" name="type" value="depense" onchange="this.form.submit()" class="hidden" <?= $typeSelected === 'depense' ? 'checked' : '' ?>>
                            <span class="flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                                </svg>
                                Dépense
                            </span>
                        </label>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row gap-4 items-end">
                    <div class="flex-grow">
                        <label for="categorie" class="block text-sm font-semibold mb-3 text-gray-700">Sélectionner une catégorie :</label>
                        <select name="categorie" id="categorie" class="w-full border-2 border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 p-4 rounded-xl transition-all duration-300 bg-white shadow-sm hover:shadow-md">
                            <option value="">Choisir une catégorie...</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?= $cat['id'] ?>" <?= (isset($_POST['categorie']) && $_POST['categorie'] == $cat['id']) ? 'selected' : '' ?>>
                                    <?= $cat['nom'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <button type="submit" name="afficher" class="px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 font-semibold rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        Afficher
                    </button>
                </div>
            </form>

            <!-- Results Section -->
            <?php if (isset($result)): ?>
                <div class="mt-8 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl border border-green-200 shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="p-2 bg-green-500 rounded-lg mr-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800">Total par catégorie</h3>
                    </div>
                    <p class="text-4xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                        <?= number_format($result ?? 0, 2) ?> <span class="text-lg font-normal text-gray-600">Dh</span>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
